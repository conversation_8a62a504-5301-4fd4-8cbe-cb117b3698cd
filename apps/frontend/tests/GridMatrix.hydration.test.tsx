/**
 * GridMatrix Hydration 测试
 * 测试组件在hydration期间处理null值的能力
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { GridMatrix } from '@/components/grid-system/GridMatrix/GridMatrix';
import type { CellData } from '@/lib/types/grid';

// Mock useGridData hook to simulate hydration states
vi.mock('@/components/grid-system/hooks/useGridData', () => ({
  useGridData: vi.fn(),
}));

// Mock useGridAnimation hook
vi.mock('@/components/grid-system/hooks/useGridAnimation', () => ({
  useGridAnimation: vi.fn(() => ({
    animationClass: '',
    isAnimating: false,
    animationStyle: {},
    getScaleStyle: vi.fn(),
    cssVariables: {},
  })),
}));

import { useGridData } from '@/components/grid-system/hooks/useGridData';

const mockUseGridData = useGridData as ReturnType<typeof vi.fn>;

describe('GridMatrix Hydration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该在hydration期间正确处理null值', () => {
    // 模拟hydration期间的状态 - cells包含null值
    const nullCells: (CellData | null)[][] = Array(3).fill(null).map(() => 
      Array(3).fill(null)
    );

    mockUseGridData.mockReturnValue({
      cells: nullCells as CellData[][],
      isLoading: false,
      error: null,
      refreshData: vi.fn(),
      isCellActive: vi.fn(() => false),
      getCellColor: vi.fn(() => '#f3f4f6'),
      getCellContent: vi.fn(() => ''),
    });

    // 渲染组件不应该抛出错误
    expect(() => {
      render(
        <GridMatrix
          config={{
            size: 20,
            cellShape: 'square',
            displayMode: 'value',
            gap: 1,
            padding: 10,
            fontSize: 12,
            scale: { enabled: false, factor: 1 },
            animation: { enabled: false, duration: 300 },
          }}
        />
      );
    }).not.toThrow();

    // 应该渲染占位符div而不是GridCell组件
    const placeholders = screen.getAllByRole('grid');
    expect(placeholders).toHaveLength(1);
  });

  it('应该在hydration完成后正确渲染真实数据', () => {
    // 模拟hydration完成后的状态 - cells包含真实数据
    const realCells: CellData[][] = [
      [
        {
          id: 'cell-0-0',
          row: 0,
          col: 0,
          x: -1,
          y: 1,
          index: 0,
          color: '#ff0000',
          colorMappingValue: 1,
          level: 1,
          group: 1,
          isActive: false,
          number: 1,
        },
        {
          id: 'cell-0-1',
          row: 0,
          col: 1,
          x: 0,
          y: 1,
          index: 1,
          color: '#00ff00',
          colorMappingValue: 2,
          level: 1,
          group: 1,
          isActive: false,
          number: 2,
        },
      ],
    ];

    mockUseGridData.mockReturnValue({
      cells: realCells,
      isLoading: false,
      error: null,
      refreshData: vi.fn(),
      isCellActive: vi.fn(() => false),
      getCellColor: vi.fn(() => '#f3f4f6'),
      getCellContent: vi.fn(() => ''),
    });

    render(
      <GridMatrix
        config={{
          size: 20,
          cellShape: 'square',
          displayMode: 'value',
          gap: 1,
          padding: 10,
          fontSize: 12,
          scale: { enabled: false, factor: 1 },
          animation: { enabled: false, duration: 300 },
        }}
      />
    );

    // 应该渲染网格容器
    const grid = screen.getByRole('grid');
    expect(grid).toBeInTheDocument();
  });

  it('应该正确处理混合状态（部分null，部分真实数据）', () => {
    // 模拟部分hydration状态
    const mixedCells: (CellData | null)[][] = [
      [
        {
          id: 'cell-0-0',
          row: 0,
          col: 0,
          x: -1,
          y: 1,
          index: 0,
          color: '#ff0000',
          colorMappingValue: 1,
          level: 1,
          group: 1,
          isActive: false,
          number: 1,
        },
        null, // 部分数据仍为null
      ],
      [null, null], // 整行为null
    ];

    mockUseGridData.mockReturnValue({
      cells: mixedCells as CellData[][],
      isLoading: false,
      error: null,
      refreshData: vi.fn(),
      isCellActive: vi.fn(() => false),
      getCellColor: vi.fn(() => '#f3f4f6'),
      getCellContent: vi.fn(() => ''),
    });

    // 渲染组件不应该抛出错误
    expect(() => {
      render(
        <GridMatrix
          config={{
            size: 20,
            cellShape: 'square',
            displayMode: 'value',
            gap: 1,
            padding: 10,
            fontSize: 12,
            scale: { enabled: false, factor: 1 },
            animation: { enabled: false, duration: 300 },
          }}
        />
      );
    }).not.toThrow();
  });
});
