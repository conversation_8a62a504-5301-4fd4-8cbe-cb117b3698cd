/**
 * 优化的网格单元格组件
 * 🎯 核心价值：高性能的单元格渲染，支持1089个单元格的流畅显示
 * 📦 功能范围：智能重渲染、内存优化、事件优化、可访问性
 * ⚡ 性能优化：React.memo、浅比较、事件委托、虚拟化准备
 */

import React, { memo, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils/cn';
import type { CellData } from '@/lib/types/grid';
import type { CellRenderData } from '@/lib/rendering/GridRenderingEngine';

// 优化的单元格属性接口
export interface OptimizedGridCellProps {
  // 核心数据
  cell: CellData;
  renderData: CellRenderData;
  
  // 状态
  isHovered?: boolean;
  isFocused?: boolean;
  isSelected?: boolean;
  
  // 事件处理
  onClick?: (cell: CellData, event: React.MouseEvent) => void;
  onDoubleClick?: (cell: CellData, event: React.MouseEvent) => void;
  onMouseEnter?: (cell: CellData) => void;
  onMouseLeave?: (cell: CellData) => void;
  onFocus?: (cell: CellData) => void;
  onBlur?: (cell: CellData) => void;
  
  // 配置
  enableHover?: boolean;
  enableFocus?: boolean;
  enableSelection?: boolean;
  
  // 可访问性
  ariaLabel?: string;
  tabIndex?: number;
}

// 单元格样式计算
const useCellStyles = (renderData: CellRenderData, isHovered: boolean, isFocused: boolean, isSelected: boolean) => {
  return useMemo(() => {
    const baseStyle = renderData.style;
    
    // 状态样式叠加
    const stateStyle: React.CSSProperties = {};
    
    if (isHovered) {
      stateStyle.transform = 'scale(1.05)';
      stateStyle.zIndex = 10;
      stateStyle.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
    }
    
    if (isFocused) {
      stateStyle.outline = '2px solid #3b82f6';
      stateStyle.outlineOffset = '1px';
    }
    
    if (isSelected) {
      stateStyle.borderColor = '#3b82f6';
      stateStyle.borderWidth = '2px';
    }
    
    return {
      ...baseStyle,
      ...stateStyle,
      transition: 'all 0.15s ease-in-out',
    };
  }, [renderData.style, isHovered, isFocused, isSelected]);
};

// 单元格类名计算
const useCellClassName = (renderData: CellRenderData, isHovered: boolean, isFocused: boolean, isSelected: boolean) => {
  return useMemo(() => {
    const classes = [renderData.className];
    
    if (isHovered) classes.push('cell-hovered');
    if (isFocused) classes.push('cell-focused');
    if (isSelected) classes.push('cell-selected');
    if (!renderData.isActive) classes.push('cell-inactive');
    if (!renderData.isVisible) classes.push('cell-hidden');
    
    return cn(...classes);
  }, [renderData.className, renderData.isActive, renderData.isVisible, isHovered, isFocused, isSelected]);
};

/**
 * 优化的网格单元格组件
 */
export const OptimizedGridCell = memo<OptimizedGridCellProps>(({
  cell,
  renderData,
  isHovered = false,
  isFocused = false,
  isSelected = false,
  onClick,
  onDoubleClick,
  onMouseEnter,
  onMouseLeave,
  onFocus,
  onBlur,
  enableHover = true,
  enableFocus = true,
  enableSelection = true,
  ariaLabel,
  tabIndex = 0,
}) => {
  // 计算样式和类名
  const cellStyle = useCellStyles(renderData, isHovered, isFocused, isSelected);
  const cellClassName = useCellClassName(renderData, isHovered, isFocused, isSelected);
  
  // 事件处理器（使用useCallback优化）
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    onClick?.(cell, event);
  }, [cell, onClick]);
  
  const handleDoubleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    onDoubleClick?.(cell, event);
  }, [cell, onDoubleClick]);
  
  const handleMouseEnter = useCallback(() => {
    if (enableHover) {
      onMouseEnter?.(cell);
    }
  }, [cell, onMouseEnter, enableHover]);
  
  const handleMouseLeave = useCallback(() => {
    if (enableHover) {
      onMouseLeave?.(cell);
    }
  }, [cell, onMouseLeave, enableHover]);
  
  const handleFocus = useCallback(() => {
    if (enableFocus) {
      onFocus?.(cell);
    }
  }, [cell, onFocus, enableFocus]);
  
  const handleBlur = useCallback(() => {
    if (enableFocus) {
      onBlur?.(cell);
    }
  }, [cell, onBlur, enableFocus]);
  
  // 键盘事件处理
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick?.(cell, event as any);
    }
  }, [cell, onClick]);
  
  // 可访问性属性
  const accessibilityProps = useMemo(() => ({
    role: 'gridcell',
    'aria-label': ariaLabel || `单元格 ${cell.x},${cell.y}`,
    'aria-selected': enableSelection ? isSelected : undefined,
    'aria-describedby': renderData.content ? `cell-content-${cell.x}-${cell.y}` : undefined,
    tabIndex: enableFocus ? tabIndex : -1,
  }), [ariaLabel, cell.x, cell.y, enableSelection, isSelected, renderData.content, enableFocus, tabIndex]);
  
  // 如果单元格不可见，返回占位符
  if (!renderData.isVisible) {
    return (
      <div
        style={{
          width: cellStyle.width,
          height: cellStyle.height,
          visibility: 'hidden',
        }}
        aria-hidden="true"
      />
    );
  }
  
  return (
    <div
      className={cellClassName}
      style={cellStyle}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      {...accessibilityProps}
    >
      {/* 单元格内容 */}
      {renderData.content && (
        <span
          id={`cell-content-${cell.x}-${cell.y}`}
          className="cell-content"
          style={{
            fontSize: 'inherit',
            fontWeight: 'inherit',
            color: 'inherit',
            userSelect: 'none',
            pointerEvents: 'none',
          }}
        >
          {renderData.content}
        </span>
      )}
      
      {/* 选择指示器 */}
      {enableSelection && isSelected && (
        <div
          className="selection-indicator"
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '6px',
            height: '6px',
            backgroundColor: '#3b82f6',
            borderRadius: '50%',
            pointerEvents: 'none',
          }}
          aria-hidden="true"
        />
      )}
      
      {/* 焦点指示器 */}
      {enableFocus && isFocused && (
        <div
          className="focus-indicator"
          style={{
            position: 'absolute',
            inset: '-2px',
            border: '2px solid #3b82f6',
            borderRadius: '2px',
            pointerEvents: 'none',
          }}
          aria-hidden="true"
        />
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只比较影响渲染的属性
  return (
    prevProps.cell.x === nextProps.cell.x &&
    prevProps.cell.y === nextProps.cell.y &&
    prevProps.renderData.color === nextProps.renderData.color &&
    prevProps.renderData.content === nextProps.renderData.content &&
    prevProps.renderData.isActive === nextProps.renderData.isActive &&
    prevProps.renderData.isVisible === nextProps.renderData.isVisible &&
    prevProps.isHovered === nextProps.isHovered &&
    prevProps.isFocused === nextProps.isFocused &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.enableHover === nextProps.enableHover &&
    prevProps.enableFocus === nextProps.enableFocus &&
    prevProps.enableSelection === nextProps.enableSelection
  );
});

OptimizedGridCell.displayName = 'OptimizedGridCell';

// 导出类型
export type { OptimizedGridCellProps };
