/**
 * 矩阵辅助工具函数
 * 🎯 职责：矩阵相关的辅助功能、默认值生成、类型获取等
 * 📦 重构来源：从 basicDataStore.ts 中提取的辅助函数
 * ✅ 工具函数：提供便捷的辅助功能，提高代码复用性
 */

import type { 
  BasicColorType, 
  GroupType, 
  ColorVisibility,
  BlackCellData 
} from '@/lib/types/matrix';
import { 
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES,
  SPECIAL_COORDINATES 
} from '@/stores/constants/matrix';
import { generateMatrixData } from './matrixUtils';

/**
 * 生成一致的颜色可见性配置
 * @param colorType 颜色类型
 * @returns 颜色可见性配置
 */
export const generateConsistentColorVisibility = (colorType: BasicColorType): ColorVisibility => {
  const availableLevels = AVAILABLE_LEVELS[colorType];
  const visibility: ColorVisibility = { showCells: true };

  // 为每个可用级别设置可见性属性
  availableLevels.forEach(level => {
    const levelKey = `showLevel${level}` as keyof ColorVisibility;
    (visibility as any)[levelKey] = true;
  });

  return visibility;
};

/**
 * 生成默认的颜色可见性配置
 * @returns 所有颜色的默认可见性配置
 */
export const generateDefaultColorVisibility = (): Record<BasicColorType, ColorVisibility> => {
  return {
    red: generateConsistentColorVisibility('red'),
    cyan: generateConsistentColorVisibility('cyan'),
    yellow: generateConsistentColorVisibility('yellow'),
    purple: generateConsistentColorVisibility('purple'),
    orange: generateConsistentColorVisibility('orange'),
    green: generateConsistentColorVisibility('green'),
    blue: generateConsistentColorVisibility('blue'),
    pink: generateConsistentColorVisibility('pink'),
    black: generateConsistentColorVisibility('black'),
  };
};

/**
 * 生成默认的组可见性配置
 * @returns 所有组的默认可见性配置
 */
export const generateDefaultGroupVisibility = (): Record<GroupType, boolean> => {
  const groups: GroupType[] = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"];
  const visibility: Record<GroupType, boolean> = {} as any;

  groups.forEach(group => {
    visibility[group] = true; // 默认所有组都可见
  });

  return visibility;
};

/**
 * 生成默认的黑色格子数据
 * @returns 默认黑色格子数据
 */
export const generateDefaultBlackCellData = (): BlackCellData => {
  return {
    coordinates: Array.from(SPECIAL_COORDINATES.entries()).map(([coords, letter]) => {
      const [x, y] = coords.split(',').map(Number);
      return { coords: [x, y] as [number, number], letter };
    }),
    visibility: true,
  };
};

/**
 * 根据坐标查找格子属于哪种颜色的工具函数 - 基于新的矩阵数据结构
 * @param x X坐标
 * @param y Y坐标
 * @returns 颜色类型，如果没有找到则返回null
 */
export const getCellColorByCoordinate = (x: number, y: number): BasicColorType | null => {
  // 首先检查黑色的特殊坐标
  const coordKey = `${x},${y}`;
  if (SPECIAL_COORDINATES.has(coordKey)) {
    return 'black';
  }

  // 使用新的矩阵数据结构查找
  const matrixData = generateMatrixData();
  const dataPoints = matrixData.byCoordinate.get(coordKey);

  if (dataPoints && dataPoints.length > 0) {
    // 返回第一个数据点的颜色（如果有多个组在同一坐标，返回第一个）
    return dataPoints[0].color;
  }

  return null; // 如果没有找到匹配的颜色，返回 null
};

/**
 * 获取所有颜色类型
 * @returns 所有颜色类型数组
 */
export const getAllColorTypes = (): BasicColorType[] => [
  'black', 'red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink'
];

/**
 * 获取所有组类型
 * @returns 所有组类型数组
 */
export const getAllGroupTypes = (): GroupType[] => [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'
];

/**
 * 获取颜色的可用级别
 * @param colorType 颜色类型
 * @returns 可用级别数组
 */
export const getAvailableLevels = (colorType: BasicColorType): number[] => {
  return AVAILABLE_LEVELS[colorType];
};

/**
 * 获取颜色值
 * @param colorType 颜色类型
 * @returns 颜色值对象
 */
export const getColorValue = (colorType: BasicColorType) => {
  return DEFAULT_COLOR_VALUES[colorType];
};
